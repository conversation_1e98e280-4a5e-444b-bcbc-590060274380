<script setup>
import { ref } from "vue";
import { NButton, NInput } from "naive-ui";
import ZdccIframe from "./components/ZdccIframe.vue";
import CxIframe from "./components/CxIframe.vue";
import CqIframe from "./components/CqIframe.vue";
import Loan from "./components/loan.vue";

const showIframe = ref(false);
const showCxIframe = ref(false);
const showCqIframe = ref(false);

const cqId = ref("");
const cxId = ref("");

const handleOpenIframe = () => {
  showIframe.value = true;
};

const handleOpenCxIframe = () => {
  showCxIframe.value = true;
};

const handleOpenCqIframe = () => {
  showCqIframe.value = true;
};

const handleUpdateIds = (ids) => {
  console.log("xxxx---", ids);
  cqId.value = ids.cqId;
  cxId.value = ids.cxId;
};
</script>

<template>
  <Loan
    :cx-id="cxId"
    :cq-id="cqId"
    @open-ruoyi="handleOpenIframe"
    @open-cx="handleOpenCxIframe"
    @open-cq="handleOpenCqIframe"
  />
  <ZdccIframe v-if="showIframe" @updateIds="handleUpdateIds" @close="showIframe = false" />
  <CxIframe v-if="showCxIframe" :cx-id="cxId" @close="showCxIframe = false" />
  <CqIframe v-if="showCqIframe" :cq-id="cqId" @close="showCqIframe = false" />
</template>

<style scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.n-button {
  margin: 16px;
}
</style>
