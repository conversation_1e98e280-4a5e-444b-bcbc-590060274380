<template>
  <div class="ruoyi-container">
    <n-modal
      v-model:show="showModal"
      :mask-closable="false"
      preset="card"
      style="width: 90vw; max-width: 90vw"
      :bordered="false"
      size="huge"
      @close="handleClose"
    >
      <div class="modal-iframe-container">
        <iframe
          id="zdzs-iframe"
          :src="iframeUrl"
          frameborder="0"
          class="modal-iframe"
          @load="onIframeLoad"
        ></iframe>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { NButton, NSpin, NModal, NInput } from "naive-ui";
import { watch } from "vue";
const emit = defineEmits(["updateIds", "close"]);

const showModal = ref(true);
const cqId = ref("");
const cxId = ref("");
const iframeUrl = ref(null);

const loadingText = ref("正在获取会话ID...");
const retryCount = ref(0);
const maxRetries = 3;
const defaultUid = "4d61ced2e4dad060c9a0c4ce9e02e12e";

/**
 * 从指定API获取sessionId
 */
const getSessionId = async (uid) => {
  try {
    // ip 和 port 按需调整
    const response = await fetch(
      `http://***********:8080/coreSystem/link/genSessionId?uid=${uid}`
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(data);
    // 假设返回的数据结构为 { code: 200, data: { sessionId: 'xxx' } }
    if (data.code === 200 && data.data && data.data.sessionId) {
      return data.data.sessionId;
    } else {
      throw new Error("无效的响应格式");
    }
  } catch (error) {
    console.error("获取sessionId失败:", error);
    throw error;
  }
};

/**
 * 获取当前用户ID
 * 这里需要根据实际项目修改获取方式
 */
const getCurrentUid = () => {
  // 示例：从本地存储获取
  return localStorage.getItem("uid") || defaultUid;

  // 或者从Vuex/Pinia store获取
  // return store.state.user.uid
};

onMounted(async () => {
  try {
    const uid = getCurrentUid();
    loadingText.value = `正在为用户 ${uid} 获取会话ID...`;

    // 获取sessionId
    const sessionId = await getSessionId(uid);
    console.log("------------>>", sessionId);

    // 组装URL
    // 指定页 仅仅做中登查询
    // const baseUrl = "http://***********?path=/weight/search&embed=true&sessionId=" + sessionId;

    // 中登查询+查重
    const baseUrl =
      " http://***********?path=/weight/embedduplicate&embed=true&sessionId=" +
      sessionId;

    //主页
    //const baseUrl = 'http://localhost?sessionId='+sessionId
    console.log(baseUrl);

    iframeUrl.value = baseUrl;
    loadingText.value = "正在加载系统...";
  } catch (error) {
    console.error("初始化失败:", error);

    if (retryCount.value < maxRetries) {
      retryCount.value++;
      loadingText.value = `尝试重新连接 (${retryCount.value}/${maxRetries})...`;
      setTimeout(() => onMounted(), 2000 * retryCount.value); // 指数退避重试
    } else {
      loadingText.value = "系统连接失败";
    }
  }
});

const onIframeLoad = () => {
  console.log("Iframe加载完成");
};

const handleClose = () => {
  showModal.value = false;
  emit('close');
  console.log("-->查重编号:", cqId.value);
};

//准备数据，准备 客户信息、租赁物信息、查询与查重的条件
const prepareData = () => {
  return {
    parameter: {
      reason: "01", //查询原因 01贷前 02 贷中 03 贷后
      matchType: "lease", //lease 租赁物  keyword 关键字
      queryType: "company", //company 公司
    },
    customer: {
      name: "xxx", //名称
      no: "x0001", //信贷系统客户编号
      role:"lessee"  // lessee 承租人 guarantor 担保人
    },
    //租赁物列表
    leases: [
        {
          id: "EQ202507200001", // 标识
          projectId: "PRJ202507001", // 项目编号
          contractId: "CTR202507001", // 合同编号
          deviceType: "挖掘机", // 设备类型
          equipName: "小型液压挖掘机", // 设备名称
          brand: "卡特彼勒", // 品牌
          model: "CAT305E2", // 型号/规格
          equipNum: "2", // 数量
          price: "250000", // 单价
          unit: "台", // 单位
          equipPrice: "500000", // 交易价格
          total: "500000", // 设备原值
          nowTotal: "480000", // 设备现值
          vndrName: "山东鼎力机械有限公司", // 供应商名称
          manufacturerName: "卡特彼勒中国", // 制造商名称
          equipDeliveryPlace: "济南仓库", // 交付地点
          equipDeliveryDate: "2025-07-01", // 交付时间
          equipPlace: "济南项目部", // 设备设置地址
          frameNumber: "FRM123456789", // 车架号
          engineNumber: "ENG987654321", // 发动机号
          plateNumber: "鲁A12345", // 车牌号
          equipType: "施工设备", // 租赁物类型
          vinno: "VIN123456789", // 车架号
          equipmentid: "EQP001", // 设备id
          equipmentNature: "租赁", // 租赁物性质
          equipmentType: "重型机械", // 租赁物分类
          equipmentNumber: "REG202507001", // 登记编码
          manufactureFactoryName: "卡特彼勒青州工厂", // 生产厂家名称
          originalValue: "500000", // 账面原值
          netValue: "480000.00", // 账面净值
          assessNetValue: "470000.00", // 评估净值
          originalPurchaseDate: "2023-09-01", // 原购置日期
          transferPrice: "460000.00", // 转让价格
          equipNumber: "EQU20250720001", // 设备唯一识别码
          invoiceNo: "INV202507001", // 发票号码
        }
    ],
    //关键字列表  没有关键字 就不传
    keywords:[
      {
        keyword:'aaaa'
      },
      {
        keyword:'bbbb'
      },
      {
        keyword:'cccc'
      }
    ]
  };
};

//加载完成后，监听中登助手界面是否完成初始化，如果已完成，就传递参数到zdzs-iframe
window.addEventListener("message", function (event) {
  // 同时处理 cqId 和 cxId 的接收
  console.log("message", event.data);
  if (event.data && (event.data.cqId || event.data.cxId)) {
    if (event.data.cqId) {
      cqId.value = event.data.cqId;
      console.log("接收到iframe传递的cqId:", event.data.cqId);
    }
    if (event.data.cxId) {
      cxId.value = event.data.cxId;
      console.log("接收到iframe传递的cxId:", event.data.cxId);
    }
    // 通过事件把两个值都传递给父组件
    emit("updateIds", { cqId: cqId.value, cxId: cxId.value });
    return;
  }

  if (event.data && event.data.ready) {
    // 子页面已准备好，可以发业务消息
    const iframe = document.getElementById("zdzs-iframe");
    const data = prepareData();

    // 延迟5秒发送消息，用于延迟测试
    setTimeout(() => {
      iframe.contentWindow.postMessage(data, "*");
      console.log("延迟5秒后发送消息到iframe");
    }, 5000);
  }
});
</script>

<style scoped>
.ruoyi-container {
  position: relative;
}

.modal-iframe-container {
  position: relative;
  height: 80vh;
  width: 100%;
}

.modal-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.back-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-content {
  text-align: center;
}

.loading-content p {
  margin-top: 16px;
  color: #333;
  font-size: 16px;
}
</style>
