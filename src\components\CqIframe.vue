<template>
  <div class="ruoyi-container">
    <n-modal
      v-model:show="showModal"
      :mask-closable="false"
      preset="card"
      style="width: 90vw; max-width: 90vw"
      :bordered="false"
      size="huge"
      @close="handleClose"
    >
      <div class="modal-iframe-container">
        <iframe
          id="zdzs-iframe"
          :src="iframeUrl"
          frameborder="0"
          class="modal-iframe"
          @load="onIframeLoad"
        ></iframe>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { NButton, NSpin, NModal, NInput } from "naive-ui";
import { watch } from "vue";

const props = defineProps({
  cqId: {
    type: String,
    default: "",
  },
});
const showModal = ref(true);
const iframeUrl = ref(null);

const loadingText = ref("正在获取会话ID...");
const retryCount = ref(0);
const maxRetries = 3;
const defaultUid = "4d61ced2e4dad060c9a0c4ce9e02e12e";

/**
 * 从指定API获取sessionId
 */
const getSessionId = async (uid) => {
  try {
    // ip 和 port 按需调整
    const response = await fetch(
      `http://***********:8080/coreSystem/link/genSessionId?uid=${uid}`
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(data);
    // 假设返回的数据结构为 { code: 200, data: { sessionId: 'xxx' } }
    if (data.code === 200 && data.data && data.data.sessionId) {
      return data.data.sessionId;
    } else {
      throw new Error("无效的响应格式");
    }
  } catch (error) {
    console.error("获取sessionId失败:", error);
    throw error;
  }
};

/**
 * 获取当前用户ID
 * 这里需要根据实际项目修改获取方式
 */
const getCurrentUid = () => {
  // 示例：从本地存储获取
  return localStorage.getItem("uid") || defaultUid;

  // 或者从Vuex/Pinia store获取
  // return store.state.user.uid
};

const emit = defineEmits(["close"]);

onMounted(async () => {
  try {
    const uid = getCurrentUid();
    loadingText.value = `正在为用户 ${uid} 获取会话ID...`;

    // 获取sessionId
    const sessionId = await getSessionId(uid);

    const _id = props.cxId || 636;
    // 中登查询明细
    const baseUrl =
      " http://***********?path=/zdw/cqdata-detail/index/" +
      _id +
      "&embed=true&sessionId=" +
      sessionId;

    console.log(baseUrl);

    iframeUrl.value = baseUrl;
    loadingText.value = "正在加载系统...";
  } catch (error) {
    console.error("初始化失败:", error);

    if (retryCount.value < maxRetries) {
      retryCount.value++;
      loadingText.value = `尝试重新连接 (${retryCount.value}/${maxRetries})...`;
      setTimeout(() => onMounted(), 2000 * retryCount.value); // 指数退避重试
    } else {
      loadingText.value = "系统连接失败";
    }
  }
});

const onIframeLoad = () => {
  console.log("Iframe加载完成");
};

const handleClose = () => {
  showModal.value = false;
  emit('close');
};
</script>

<style scoped>
.ruoyi-container {
  position: relative;
}

.modal-iframe-container {
  position: relative;
  height: 80vh;
  width: 100%;
}

.modal-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.back-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-content {
  text-align: center;
}

.loading-content p {
  margin-top: 16px;
  color: #333;
  font-size: 16px;
}
</style>
