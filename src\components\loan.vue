<script setup>
import { ref } from 'vue';
import { NButton, NCard, NInput, NGrid, NGridItem, NTag, NLayout, NLayoutHeader, NLayoutContent, NMenu, NDivider, NDescriptions, NDescriptionsItem } from 'naive-ui';

const menuOptions = [
  { label: '客户管理', key: 'customer' },
  { label: '贷款申请', key: 'loan' },
  { label: '审批流程', key: 'approval' },
  { label: '放款管理', key: 'disbursement' },
  { label: '还款管理', key: 'repayment' },
  { label: '报表中心', key: 'report' },
];

const emit = defineEmits([
  'open-ruoyi',
  'open-cx',
  'open-cq',
]);

// 这些id可以通过props传递，也可以mock
const props = defineProps({
  cxId: { type: String, default: '' },
  cqId: { type: String, default: '' },
});
</script>

<template>
  <n-layout style="height: 100vh; background: #f5f7fa;">
    <n-layout-header bordered style="background: #fff;">
      <div style="display: flex; align-items: center; height: 56px;">
        <div style="font-size: 22px; font-weight: bold; color: #2d8cf0; margin-right: 32px;">业务系统</div>
        <n-menu mode="horizontal" :options="menuOptions" style="flex: 1;" />
      </div>
    </n-layout-header>
    <n-layout-content style="padding: 32px;">
      <n-card title="客户信息" :bordered="false" style="max-width: 700px; margin: 0 auto;">
        <n-divider />
        <div style="display: flex; gap: 16px; margin-top: 16px;">
          <n-button type="primary" @click="$emit('open-ruoyi')">查询+查重</n-button>
          <n-button v-if="cxId" type="info" @click="$emit('open-cx')">查询结果(编号:{{ cxId }})</n-button>
          <n-button v-if="cqId" type="warning" @click="$emit('open-cq')">查重结果(编号:{{ cqId }})</n-button>
        </div>
      </n-card>
    </n-layout-content>
  </n-layout>
</template>

<style scoped>
</style> 